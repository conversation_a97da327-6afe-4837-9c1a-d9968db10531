#!/usr/bin/env python3
"""
Script to generate JSON lines file with prompt-response pairs and images from scraped content and extraction results.

This script:
1. Reads scrape content data (contains raw product data with image URLs)
2. Reads extraction results data (contains processed/extracted content for responses)
3. Populates prompt template with scraped data
4. Extracts image URLs from product data
5. Generates JSONL file with prompt-response pairs and images

Usage:
    python generate_prompt_response_pairs.py scrape_content.json extraction_results.json output.jsonl
"""

import json
import sys
import base64
import requests
from typing import Dict, List, Any, Optional
import argparse
from pathlib import Path


def load_json_file(file_path: str) -> Dict[str, Any]:
    """Load and parse JSON file."""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            return json.load(f)
    except Exception as e:
        print(f"Error loading {file_path}: {e}")
        sys.exit(1)


def load_prompt_template(template_path: str = "prompt.txt") -> str:
    """Load the prompt template."""
    try:
        with open(template_path, 'r', encoding='utf-8') as f:
            return f.read()
    except Exception as e:
        print(f"Error loading prompt template {template_path}: {e}")
        sys.exit(1)


def load_response_schema(schema_path: str = "response_schema.json") -> str:
    """Load the response schema as string."""
    try:
        with open(schema_path, 'r', encoding='utf-8') as f:
            schema_data = json.load(f)
            return json.dumps(schema_data, indent=2)
    except Exception as e:
        print(f"Error loading response schema {schema_path}: {e}")
        sys.exit(1)


def download_image_as_base64(url: str) -> Optional[str]:
    """Download image from URL and convert to base64."""
    try:
        response = requests.get(url, timeout=30)
        response.raise_for_status()

        # Convert to base64
        image_base64 = base64.b64encode(response.content).decode('utf-8')

        # Get content type from headers or guess from URL
        content_type = response.headers.get('content-type', 'image/jpeg')

        # Return as data URL
        return f"data:{content_type};base64,{image_base64}"

    except Exception as e:
        print(f"Warning: Failed to download image {url}: {e}")
        return None


def extract_images_from_product_data(product_data: Dict[str, Any]) -> List[str]:
    """Extract image URLs from product data."""
    images = []

    # Try to get images_url field first
    if 'images_url' in product_data and isinstance(product_data['images_url'], list):
        images.extend(product_data['images_url'])

    # Fallback to images field if images_url not found
    elif 'images' in product_data and isinstance(product_data['images'], list):
        for img in product_data['images']:
            if isinstance(img, dict):
                # Try different image size fields
                for size_key in ['l', 'm', 's']:  # large, medium, small
                    if size_key in img:
                        # Construct full Amazon image URL
                        img_filename = img[size_key]
                        full_url = f"https://m.media-amazon.com/images/I/{img_filename}"
                        images.append(full_url)
                        break

    return images


def populate_prompt_template(template: str, response_schema: str, product_data: Dict[str, Any],
                           markdown_content: str, enriched_data: str = "") -> str:
    """Populate the prompt template with actual data."""

    # Convert product_data to JSON string if it's a dict
    if isinstance(product_data, dict):
        product_data_str = json.dumps(product_data, indent=2)
    else:
        product_data_str = str(product_data)

    # Replace template placeholders
    populated_prompt = template.replace("{{response_schema}}", response_schema)
    populated_prompt = populated_prompt.replace("{{product_data}}", product_data_str)
    populated_prompt = populated_prompt.replace("{{markdown_content}}", markdown_content)
    populated_prompt = populated_prompt.replace("{{enriched_data}}", enriched_data)

    return populated_prompt


def create_jsonl_entry(prompt: str, response: str, images: List[str]) -> Dict[str, Any]:
    """Create a single JSONL entry with prompt, response, and images."""
    return {
        "prompt": prompt,
        "response": response,
        "images": images
    }


def process_scrape_content_and_extraction_results(scrape_content: Dict[str, Any],
                                                extraction_results: Dict[str, Any],
                                                prompt_template: str,
                                                response_schema: str,
                                                download_images: bool = False,
                                                max_entries: Optional[int] = None) -> List[Dict[str, Any]]:
    """Process scrape content and extraction results to create prompt-response pairs."""

    jsonl_entries = []

    # Get the list of scrape content entries
    scrape_entries = scrape_content.get("select * from scrape_content sc where asin in (select asin from tasks where id in (select task_id from extraction_results))", [])

    # Get extraction results entries
    extraction_entries = extraction_results.get("extraction_results", [])

    # Create a mapping of task_id to extraction result
    extraction_map = {entry["task_id"]: entry for entry in extraction_entries}

    print(f"Processing {len(scrape_entries)} scrape entries and {len(extraction_entries)} extraction results")

    # Limit entries if max_entries is specified
    if max_entries:
        scrape_entries = scrape_entries[:max_entries]
        print(f"Limited to {len(scrape_entries)} entries for processing")

    for scrape_entry in scrape_entries:
        task_id = scrape_entry.get("task_id")

        # Find corresponding extraction result
        extraction_entry = extraction_map.get(task_id)
        if not extraction_entry:
            print(f"Warning: No extraction result found for task_id {task_id}")
            continue

        try:
            # Parse product_data JSON string
            product_data_str = scrape_entry.get("product_data", "{}")
            product_data = json.loads(product_data_str) if isinstance(product_data_str, str) else product_data_str

            # Get markdown content
            markdown_content = scrape_entry.get("markdown_content", "")

            # Extract images from product data
            image_urls = extract_images_from_product_data(product_data)

            # Download images and convert to base64 if requested
            if download_images:
                print(f"  Downloading {len(image_urls)} images for task_id {task_id}...")
                images = []
                for url in image_urls:
                    base64_image = download_image_as_base64(url)
                    if base64_image:
                        images.append(base64_image)
                    else:
                        images.append(url)  # Fallback to URL if download fails
            else:
                # Just store the URLs
                images = image_urls

            # Get the extraction result as response
            extracted_json_str = extraction_entry.get("extracted_json", "{}")
            if isinstance(extracted_json_str, str):
                # Parse and reformat the extracted JSON
                extracted_data = json.loads(extracted_json_str)
                response = json.dumps(extracted_data, indent=2)
            else:
                response = json.dumps(extracted_json_str, indent=2)

            # Populate prompt template
            prompt = populate_prompt_template(
                prompt_template,
                response_schema,
                product_data,
                markdown_content
            )

            # Create JSONL entry
            entry = create_jsonl_entry(prompt, response, images)
            jsonl_entries.append(entry)

            print(f"Processed task_id {task_id} with {len(images)} images")

        except Exception as e:
            print(f"Error processing task_id {task_id}: {e}")
            continue

    return jsonl_entries


def save_jsonl_file(entries: List[Dict[str, Any]], output_path: str):
    """Save entries to JSONL file."""
    try:
        with open(output_path, 'w', encoding='utf-8') as f:
            for entry in entries:
                f.write(json.dumps(entry, ensure_ascii=False) + '\n')
        print(f"Successfully saved {len(entries)} entries to {output_path}")
    except Exception as e:
        print(f"Error saving JSONL file {output_path}: {e}")
        sys.exit(1)


def main():
    parser = argparse.ArgumentParser(description="Generate prompt-response pairs from scrape content and extraction results")
    parser.add_argument("scrape_content_file", help="Path to scrape content JSON file")
    parser.add_argument("extraction_results_file", help="Path to extraction results JSON file")
    parser.add_argument("output_file", help="Path to output JSONL file")
    parser.add_argument("--prompt-template", default="prompt.txt", help="Path to prompt template file")
    parser.add_argument("--response-schema", default="response_schema.json", help="Path to response schema file")
    parser.add_argument("--download-images", action="store_true", help="Download images and convert to base64 (warning: slow for large datasets)")
    parser.add_argument("--max-entries", type=int, help="Maximum number of entries to process (for testing)")

    args = parser.parse_args()

    # Validate input files exist
    for file_path in [args.scrape_content_file, args.extraction_results_file, args.prompt_template, args.response_schema]:
        if not Path(file_path).exists():
            print(f"Error: File {file_path} does not exist")
            sys.exit(1)

    # Load data
    print("Loading data files...")
    scrape_content = load_json_file(args.scrape_content_file)
    extraction_results = load_json_file(args.extraction_results_file)
    prompt_template = load_prompt_template(args.prompt_template)
    response_schema = load_response_schema(args.response_schema)

    # Process data
    print("Processing data...")
    jsonl_entries = process_scrape_content_and_extraction_results(
        scrape_content,
        extraction_results,
        prompt_template,
        response_schema,
        download_images=args.download_images,
        max_entries=args.max_entries
    )

    # Save results
    print("Saving results...")
    save_jsonl_file(jsonl_entries, args.output_file)

    print("Done!")


if __name__ == "__main__":
    main()
